# FeneChain Architecture Diagram

```mermaid
graph TB
    subgraph "Certificate Authority - CA"
        CA[CA<br/>- System Initialization<br/>- Key Generation<br/>- Entity Registration<br/>- Attribute Management]
    end
    
    subgraph "Energy Nodes - IIoT"
        subgraph "Energy Purchaser"
            EP[Energy Purchaser EP<br/>- Private key: xep<br/>- Public key: pubkep<br/>- Wallet address]
            SM1[Smart Meter]
        end
        
        subgraph "Energy Seller"
            ES[Energy Seller ES<br/>- Private key: xes<br/>- Public key: pubkes<br/>- User secret key: uskes<br/>- Attributes]
            SM2[Smart Meter<br/>Generate Bill B]
        end
    end
    
    subgraph "Energy Trading Infrastructure"
        subgraph "Energy Broker - EB"
            EB[Energy Broker<br/>- Verify transactions<br/>- Broadcast requests<br/>- Maintain blacklist BLeb<br/>- Generate qk]
            AC[Access Control<br/>- Verify attributes<br/>- Generate tqeb]
            TC[Timed Commitments<br/>- Ensure fairness<br/>- Handle deposits]
        end
        
        subgraph "Consortium Blockchain - CBC"
            BC[Consortium Blockchain<br/>- PBFT Consensus<br/>- Store transactions<br/>- Smart contracts]
            SC1[Smart Contract 1<br/>Anonymous Authentication]
            SC2[Smart Contract 2<br/>Timed Commitments]
        end
    end
    
    subgraph "Communication"
        API[Public API]
        SEC[Secure Channels]
    end
    
    subgraph "Legend"
        L1[Component]
        L2[→ Data Flow]
    end
    
    %% Registration Phase
    CA -.->|1. Register<br/>Keys and uskes| ES
    CA -.->|1. Register<br/>Keys and tokens| EP
    CA -.->|1. Register<br/>prikEB, pubkEB| EB
    
    %% Energy Trading Flow
    EP -->|2. Energy Request<br/>ERep| API
    API --> EB
    EB -->|3. Verify and Broadcast<br/>cteb, deep, prep, T| ES
    
    %% Transaction Phase
    ES ==>|4. Deposit<br/>Txes_dep| BC
    EP ==>|5. Payment<br/>Txep| BC
    ES -->|6. Energy Transfer<br/>via pubkep| EP
    ES ==>|7. Commitment<br/>Txes_com| BC
    
    %% Smart Meter Operations
    SM1 -.-> EP
    SM2 -.->|Bill B| ES
    
    %% Blockchain Operations
    EB <--> BC
    AC --> EB
    TC --> BC
    SC1 -.-> BC
    SC2 -.-> BC
    
    %% Dispute Resolution
    EP -->|8. Complaint<br/>if cheating| EB
    EB -->|Add to BLeb| AC
    BC -->|Return deposits<br/>if honest| ES
```

## Architecture Components

### What are the components?

1. **Certificate Authority (CA)**
   - Initializes FeneChain with security parameter 1^k
   - Generates group G, prime order q, generator g, hash functions H1, H2, H3
   - Registers all entities and distributes cryptographic keys
   - Manages attribute universe AU and version keys for access control

2. **Energy Purchaser (EP)**
   - IIoT node requesting energy with demand deep and bid price prep
   - Maintains wallet with private key xep and public key pubkep=(yep, hep1, hep2)
   - Uses anonymous authentication to protect privacy
   - Sends payment transactions with tokens tkep

3. **Energy Seller (ES)**
   - IIoT node providing surplus energy to the network
   - Holds user secret key uskes with verified attributes for access control
   - Deposits tokens tkes as guarantee before trading
   - Creates commitment Commes=H1(B) of energy trading bill

4. **Energy Broker (EB)**
   - Verifies energy requests using signature verification (σep)
   - Broadcasts valid requests with trading qualification string tqeb
   - Maintains local blacklist BLeb for malicious sellers
   - Acts as blockchain miner using PBFT consensus mechanism

5. **Smart Meters (SM)**
   - Tamper-proof devices embedded in energy nodes
   - Record amount of transacted energy
   - Generate energy trading bill B with transaction details

6. **Consortium Blockchain (CBC)**
   - Distributed ledger maintained by multiple EBs using PBFT
   - Stores three types of transactions:
     - Deposit transactions (Txes_dep)
     - Payment transactions (Txep)
     - Commitment transactions (Txes_com)
   - Implements smart contracts for anonymous authentication and timed commitments

7. **Access Control & Timed Commitments Components**
   - Fine-grained attribute-based access control mechanism
   - Timed commitment scheme to guarantee verifiable fairness
   - Prevents cheating attacks from malicious energy sellers

### How do the components interact?

The interaction follows an 8-step process:

1. **Registration Phase**: All entities (EP, ES, EB) register with CA to obtain cryptographic credentials including keys and attributes

2. **Energy Request**: EP encrypts energy demand (deep) and bid price (prep) into ctep, creates signature σep, and sends ERep=(pubkep, ctep, σep) through API to EB

3. **Verification & Broadcast**: EB verifies the signature and broadcasts encrypted trading qualification cteb along with deep, prep, and time period T to qualified ES

4. **Deposit Transaction**: ES deposits tokens tkes on blockchain as guarantee by sending Txes_dep=(pubkes, ctes, tkes, σes)

5. **Payment Transaction**: EP pays negotiated price to ES via blockchain with Txep=("Payment", pubkes, tkep, pubkep, τep)

6. **Energy Transfer**: ES transfers the negotiated amount of energy to EP using EP's public key address

7. **Commitment Creation**: ES creates commitment Commes of bill B and posts Txes_com=("Commitment", pubkes, Commes, τes) on blockchain

8. **Dispute Resolution**: If ES cheats (doesn't transfer energy), EP files complaint to EB; if honest, ES gets deposits back after time T expires

### A legend is required

**Legend:**
- **Component**: Rectangular boxes representing system components (CA, EP, ES, EB, CBC, etc.)
- **Data Flow**: Arrows representing different types of interactions and data flow between components

The architecture ensures **verifiable fairness** through timed commitments, **privacy** through anonymous authentication, and **security** through access control and blockchain immutability.